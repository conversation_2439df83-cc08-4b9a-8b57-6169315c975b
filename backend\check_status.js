const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'data', 'erp.db');
const db = new sqlite3.Database(dbPath);

console.log('检查销售订单状态...');

db.all("SELECT id, order_no, status FROM sales_orders ORDER BY id", (err, rows) => {
  if (err) {
    console.error('查询错误:', err);
    return;
  }
  
  console.log('销售订单:');
  rows.forEach(row => {
    console.log(`ID: ${row.id}, 订单号: ${row.order_no}, 状态: ${row.status}`);
  });
  
  console.log('\n检查生产计划状态...');
  
  db.all("SELECT id, plan_no, status FROM production_plans ORDER BY id", (err, rows) => {
    if (err) {
      console.error('查询错误:', err);
      return;
    }
    
    console.log('生产计划:');
    rows.forEach(row => {
      console.log(`ID: ${row.id}, 计划号: ${row.plan_no}, 状态: ${row.status}`);
    });
    
    db.close();
  });
});
