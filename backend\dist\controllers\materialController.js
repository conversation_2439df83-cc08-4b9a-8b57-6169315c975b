"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getMaterials = getMaterials;
exports.getMaterial = getMaterial;
exports.createMaterial = createMaterial;
exports.updateMaterial = updateMaterial;
exports.deleteMaterial = deleteMaterial;
const database_1 = require("../models/database");
const inventoryUtils_1 = require("../utils/inventoryUtils");
// 获取原材料列表
async function getMaterials(req, res) {
    try {
        const { page = 1, limit = 10, search = '' } = req.query;
        const offset = (Number(page) - 1) * Number(limit);
        const db = (0, database_1.getDatabase)();
        // 构建查询条件
        let whereClause = "WHERE status = 'active'";
        const params = [];
        if (search) {
            whereClause += " AND (code LIKE ? OR name LIKE ? OR specification LIKE ?)";
            const searchPattern = `%${search}%`;
            params.push(searchPattern, searchPattern, searchPattern);
        }
        // 获取总数
        const countQuery = `SELECT COUNT(*) as total FROM materials ${whereClause}`;
        const totalResult = await new Promise((resolve, reject) => {
            db.get(countQuery, params, (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        // 获取数据
        const dataQuery = `
      SELECT * FROM materials ${whereClause}
      ORDER BY created_at DESC
      LIMIT ? OFFSET ?
    `;
        const materials = await new Promise((resolve, reject) => {
            db.all(dataQuery, [...params, Number(limit), offset], (err, rows) => {
                if (err)
                    reject(err);
                else
                    resolve(rows);
            });
        });
        const response = {
            data: materials,
            total: totalResult.total,
            page: Number(page),
            limit: Number(limit),
            totalPages: Math.ceil(totalResult.total / Number(limit))
        };
        res.json({
            success: true,
            message: '获取原材料列表成功',
            data: response
        });
    }
    catch (error) {
        console.error('获取原材料列表错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
// 获取单个原材料
async function getMaterial(req, res) {
    try {
        const { id } = req.params;
        const db = (0, database_1.getDatabase)();
        const material = await new Promise((resolve, reject) => {
            db.get('SELECT * FROM materials WHERE id = ?', [id], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        if (!material) {
            return res.status(404).json({
                success: false,
                message: '原材料不存在'
            });
        }
        res.json({
            success: true,
            message: '获取原材料成功',
            data: material
        });
    }
    catch (error) {
        console.error('获取原材料错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
// 创建原材料
async function createMaterial(req, res) {
    try {
        const { code, name, specification = '', unit, cost_price = 0, stock_min = 0, stock_max = 0, current_stock = 0 } = req.body;
        // 验证必填字段
        if (!code || !name || !unit) {
            return res.status(400).json({
                success: false,
                message: '编码、名称和单位不能为空'
            });
        }
        const db = (0, database_1.getDatabase)();
        // 检查编码是否已存在
        const existingMaterial = await new Promise((resolve, reject) => {
            db.get('SELECT * FROM materials WHERE code = ?', [code], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        if (existingMaterial) {
            return res.status(409).json({
                success: false,
                message: '原材料编码已存在'
            });
        }
        // 创建原材料
        const materialId = await new Promise((resolve, reject) => {
            db.run(`INSERT INTO materials (code, name, specification, unit, cost_price, stock_min, stock_max, current_stock)
         VALUES (?, ?, ?, ?, ?, ?, ?, ?)`, [code, name, specification, unit, cost_price, stock_min, stock_max, current_stock], function (err) {
                if (err)
                    reject(err);
                else
                    resolve(this.lastID);
            });
        });
        // 检查库存预警
        await (0, inventoryUtils_1.checkInventoryAlerts)('material', materialId);
        res.status(201).json({
            success: true,
            message: '原材料创建成功',
            data: { id: materialId }
        });
    }
    catch (error) {
        console.error('创建原材料错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
// 更新原材料
async function updateMaterial(req, res) {
    try {
        const { id } = req.params;
        const updateData = req.body;
        const db = (0, database_1.getDatabase)();
        // 检查原材料是否存在
        const existingMaterial = await new Promise((resolve, reject) => {
            db.get('SELECT * FROM materials WHERE id = ?', [id], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        if (!existingMaterial) {
            return res.status(404).json({
                success: false,
                message: '原材料不存在'
            });
        }
        // 如果更新编码，检查是否重复
        if (updateData.code && updateData.code !== existingMaterial.code) {
            const codeExists = await new Promise((resolve, reject) => {
                db.get('SELECT * FROM materials WHERE code = ? AND id != ?', [updateData.code, id], (err, row) => {
                    if (err)
                        reject(err);
                    else
                        resolve(row);
                });
            });
            if (codeExists) {
                return res.status(409).json({
                    success: false,
                    message: '原材料编码已存在'
                });
            }
        }
        // 构建更新语句
        const updateFields = [];
        const updateValues = [];
        for (const [key, value] of Object.entries(updateData)) {
            if (value !== undefined) {
                updateFields.push(`${key} = ?`);
                updateValues.push(value);
            }
        }
        if (updateFields.length === 0) {
            return res.status(400).json({
                success: false,
                message: '没有提供更新数据'
            });
        }
        updateFields.push('updated_at = CURRENT_TIMESTAMP');
        updateValues.push(id);
        await new Promise((resolve, reject) => {
            db.run(`UPDATE materials SET ${updateFields.join(', ')} WHERE id = ?`, updateValues, (err) => {
                if (err)
                    reject(err);
                else
                    resolve();
            });
        });
        // 检查库存预警
        await (0, inventoryUtils_1.checkInventoryAlerts)('material', parseInt(id));
        res.json({
            success: true,
            message: '原材料更新成功'
        });
    }
    catch (error) {
        console.error('更新原材料错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
// 删除原材料（软删除）
async function deleteMaterial(req, res) {
    try {
        const { id } = req.params;
        const db = (0, database_1.getDatabase)();
        // 检查原材料是否存在
        const existingMaterial = await new Promise((resolve, reject) => {
            db.get('SELECT * FROM materials WHERE id = ?', [id], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        if (!existingMaterial) {
            return res.status(404).json({
                success: false,
                message: '原材料不存在'
            });
        }
        // 软删除（设置状态为inactive）
        await new Promise((resolve, reject) => {
            db.run('UPDATE materials SET status = ?, updated_at = CURRENT_TIMESTAMP WHERE id = ?', ['inactive', id], (err) => {
                if (err)
                    reject(err);
                else
                    resolve();
            });
        });
        res.json({
            success: true,
            message: '原材料删除成功'
        });
    }
    catch (error) {
        console.error('删除原材料错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
//# sourceMappingURL=materialController.js.map