"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSalesDeliveries = getSalesDeliveries;
exports.getSalesDelivery = getSalesDelivery;
exports.createSalesDelivery = createSalesDelivery;
const database_1 = require("../models/database");
const inventoryUtils_1 = require("../utils/inventoryUtils");
// 生成出库单号
function generateDeliveryNo() {
    const now = new Date();
    const year = now.getFullYear();
    const month = String(now.getMonth() + 1).padStart(2, '0');
    const day = String(now.getDate()).padStart(2, '0');
    const time = String(now.getTime()).slice(-6); // 取时间戳后6位
    return `SD${year}${month}${day}${time}`;
}
// 获取销售出库单列表
async function getSalesDeliveries(req, res) {
    try {
        const { page = 1, limit = 10, search = '', status = '' } = req.query;
        const offset = (Number(page) - 1) * Number(limit);
        const db = (0, database_1.getDatabase)();
        // 构建查询条件
        let whereClause = "WHERE 1=1";
        const params = [];
        if (search) {
            whereClause += " AND (sd.delivery_no LIKE ? OR c.name LIKE ? OR so.order_no LIKE ?)";
            const searchPattern = `%${search}%`;
            params.push(searchPattern, searchPattern, searchPattern);
        }
        if (status) {
            whereClause += " AND sd.status = ?";
            params.push(status);
        }
        // 获取总数
        const countQuery = `
      SELECT COUNT(*) as total 
      FROM sales_deliveries sd
      LEFT JOIN customers c ON sd.customer_id = c.id
      LEFT JOIN sales_orders so ON sd.sales_order_id = so.id
      ${whereClause}
    `;
        const totalResult = await new Promise((resolve, reject) => {
            db.get(countQuery, params, (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        // 获取数据
        const dataQuery = `
      SELECT 
        sd.*,
        c.name as customer_name,
        so.order_no as sales_order_no
      FROM sales_deliveries sd
      LEFT JOIN customers c ON sd.customer_id = c.id
      LEFT JOIN sales_orders so ON sd.sales_order_id = so.id
      ${whereClause}
      ORDER BY sd.created_at DESC
      LIMIT ? OFFSET ?
    `;
        const deliveries = await new Promise((resolve, reject) => {
            db.all(dataQuery, [...params, Number(limit), offset], (err, rows) => {
                if (err)
                    reject(err);
                else
                    resolve(rows);
            });
        });
        const response = {
            data: deliveries,
            total: totalResult.total,
            page: Number(page),
            limit: Number(limit),
            totalPages: Math.ceil(totalResult.total / Number(limit))
        };
        res.json({
            success: true,
            message: '获取销售出库单列表成功',
            data: response
        });
    }
    catch (error) {
        console.error('获取销售出库单列表错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
// 获取单个销售出库单详情（包含明细）
async function getSalesDelivery(req, res) {
    try {
        const { id } = req.params;
        const db = (0, database_1.getDatabase)();
        // 获取出库单主信息
        const delivery = await new Promise((resolve, reject) => {
            db.get(`
        SELECT 
          sd.*,
          c.name as customer_name,
          so.order_no as sales_order_no
        FROM sales_deliveries sd
        LEFT JOIN customers c ON sd.customer_id = c.id
        LEFT JOIN sales_orders so ON sd.sales_order_id = so.id
        WHERE sd.id = ?
      `, [id], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        if (!delivery) {
            return res.status(404).json({
                success: false,
                message: '销售出库单不存在'
            });
        }
        // 获取出库单明细
        const items = await new Promise((resolve, reject) => {
            db.all(`
        SELECT 
          sdi.*,
          p.code as product_code,
          p.name as product_name,
          p.unit
        FROM sales_delivery_items sdi
        LEFT JOIN products p ON sdi.product_id = p.id
        WHERE sdi.sales_delivery_id = ?
        ORDER BY sdi.id
      `, [id], (err, rows) => {
                if (err)
                    reject(err);
                else
                    resolve(rows);
            });
        });
        const deliveryWithItems = {
            ...delivery,
            items
        };
        res.json({
            success: true,
            message: '获取销售出库单成功',
            data: deliveryWithItems
        });
    }
    catch (error) {
        console.error('获取销售出库单错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
// 创建销售出库单
async function createSalesDelivery(req, res) {
    try {
        const { sales_order_id, delivery_date, remark = '', items } = req.body;
        // 验证必填字段
        if (!sales_order_id || !delivery_date || !items || items.length === 0) {
            return res.status(400).json({
                success: false,
                message: '销售订单、出库日期和出库明细不能为空'
            });
        }
        const db = (0, database_1.getDatabase)();
        // 检查销售订单是否存在且已审核
        const salesOrder = await new Promise((resolve, reject) => {
            db.get(`
        SELECT so.*, c.id as customer_id 
        FROM sales_orders so
        LEFT JOIN customers c ON so.customer_id = c.id
        WHERE so.id = ?
      `, [sales_order_id], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        if (!salesOrder) {
            return res.status(404).json({
                success: false,
                message: '销售订单不存在'
            });
        }
        if (salesOrder.status !== 'approved') {
            return res.status(400).json({
                success: false,
                message: '只有已审核的销售订单才能创建出库单'
            });
        }
        // 检查成品库存是否充足
        for (const item of items) {
            const product = await new Promise((resolve, reject) => {
                db.get('SELECT * FROM products WHERE id = ?', [item.product_id], (err, row) => {
                    if (err)
                        reject(err);
                    else
                        resolve(row);
                });
            });
            if (!product) {
                return res.status(404).json({
                    success: false,
                    message: `成品ID ${item.product_id} 不存在`
                });
            }
            if (product.current_stock < item.quantity) {
                return res.status(400).json({
                    success: false,
                    message: `成品 "${product.name}" 库存不足，当前库存：${product.current_stock}，需要：${item.quantity}`
                });
            }
        }
        const deliveryNo = generateDeliveryNo();
        // 计算总金额
        const totalAmount = items.reduce((sum, item) => sum + (item.quantity * item.unit_price), 0);
        // 开始事务
        await new Promise((resolve, reject) => {
            db.serialize(async () => {
                try {
                    db.run('BEGIN TRANSACTION');
                    // 创建销售出库单主表
                    const deliveryId = await new Promise((resolve, reject) => {
                        db.run(`
              INSERT INTO sales_deliveries (
                delivery_no, sales_order_id, customer_id, delivery_date, 
                total_amount, remark
              ) VALUES (?, ?, ?, ?, ?, ?)
            `, [deliveryNo, sales_order_id, salesOrder.customer_id, delivery_date, totalAmount, remark], function (err) {
                            if (err)
                                reject(err);
                            else
                                resolve(this.lastID);
                        });
                    });
                    // 创建出库单明细并更新成品库存
                    for (const item of items) {
                        const totalPrice = item.quantity * item.unit_price;
                        // 创建出库单明细
                        await new Promise((resolve, reject) => {
                            db.run(`
                INSERT INTO sales_delivery_items (
                  sales_delivery_id, product_id, quantity, unit_price, total_price
                ) VALUES (?, ?, ?, ?, ?)
              `, [deliveryId, item.product_id, item.quantity, item.unit_price, totalPrice], (err) => {
                                if (err)
                                    reject(err);
                                else
                                    resolve();
                            });
                        });
                        // 更新成品库存并记录变动（减少）
                        await (0, inventoryUtils_1.updateInventoryWithMovement)('product', item.product_id, item.quantity, 'out', 'sales_delivery', deliveryId, deliveryNo, `销售出库：${item.quantity}`, req.user?.id, false // 不使用事务，因为已在外层事务中
                        );
                    }
                    db.run('COMMIT', (err) => {
                        if (err)
                            reject(err);
                        else
                            resolve();
                    });
                }
                catch (error) {
                    db.run('ROLLBACK');
                    reject(error);
                }
            });
        });
        res.status(201).json({
            success: true,
            message: '销售出库单创建成功，库存已更新',
            data: { delivery_no: deliveryNo }
        });
    }
    catch (error) {
        console.error('创建销售出库单错误:', error);
        res.status(500).json({
            success: false,
            message: '服务器内部错误'
        });
    }
}
//# sourceMappingURL=salesDeliveryController.js.map