# ERP进销存管理系统 PRD - 第二阶段 v1.0

## 1. 产品概述

### 1.1 产品定位
面向30人小公司的轻量级ERP进销存管理系统，专注核心业务流程，操作简单，部署便捷。

### 1.2 目标用户
- 小型制造企业
- 贸易公司
- 仓储物流企业

### 1.3 核心价值
- 库存实时管控
- 采购销售流程化
- 财务数据透明化

## 2. 第二阶段功能范围

### 2.1 核心模块
```
├── 基础数据管理（扩展）
│   ├── 供应商管理
│   └── 客户管理
├── 采购管理
│   ├── 原材料采购
│   └── 商品采购（贸易）
├── 生产管理（简化版）
│   ├── 生产计划
│   └── 生产完工
└── 销售管理
    ├── 销售订单
    └── 销售出库
```

### 2.2 详细功能

#### 2.2.1 供应商管理
- 供应商基础信息维护
- 核心字段：编码、名称、联系人、电话、地址、结算方式

#### 2.2.2 客户管理
- 客户基础信息维护
- 核心字段：编码、名称、联系人、电话、地址

#### 2.2.3 原材料采购管理
- 采购订单创建、审核、执行
- 采购入库单生成
- 自动更新原材料库存

#### 2.2.4 简化生产管理
- 生产计划制定
- 原材料消耗记录
- 成品产出记录

#### 2.2.5 销售管理
- 销售订单管理
- 库存可用性检查
- 销售出库处理

## 3. 页面结构

### 3.1 第二阶段页面
```
├── 基础数据管理（扩展）
│   ├── 供应商管理
│   └── 客户管理
├── 采购管理
│   ├── 原材料采购
│   └── 商品采购（贸易）
├── 生产管理
│   ├── 生产计划
│   └── 生产完工
└── 销售管理
    ├── 销售订单列表
    └── 销售出库
```

## 4. 技术架构

### 4.1 前端技术栈
- **框架**: Vue 3 + TypeScript
- **UI组件**: Element Plus
- **状态管理**: Pinia
- **路由**: Vue Router
- **构建工具**: Vite

### 4.2 后端技术栈
- **API标准**: RESTful API
- **认证方式**: JWT Token
- **数据格式**: JSON
- **跨域处理**: CORS配置

## 5. 安全设计

### 5.1 基础安全
- 用户登录认证
- 页面访问权限控制
- 数据输入验证

### 5.2 数据安全
- 关键操作日志记录
- 数据备份机制
- 基础防SQL注入

## 6. 非功能需求

### 6.1 性能要求
- 页面加载时间 < 3秒
- 支持30个并发用户

### 6.2 兼容性
- Chrome、Firefox、Safari
- 移动端浏览器兼容

## 7. 第二阶段开发任务清单

### 7.1 基础数据扩展 (1周)

#### 供应商管理
**接口设计**:
- [ ] GET /api/suppliers - 获取供应商列表
- [ ] POST /api/suppliers - 创建供应商
- [ ] PUT /api/suppliers/:id - 更新供应商
- [ ] DELETE /api/suppliers/:id - 删除供应商

**页面开发**:
- [ ] 供应商列表页面
- [ ] 供应商新增/编辑表单
- [ ] 供应商详情查看
- [ ] 搜索和筛选功能

#### 客户管理
**接口设计**:
- [ ] GET /api/customers - 获取客户列表
- [ ] POST /api/customers - 创建客户
- [ ] PUT /api/customers/:id - 更新客户
- [ ] DELETE /api/customers/:id - 删除客户

**页面开发**:
- [ ] 客户列表页面
- [ ] 客户新增/编辑表单
- [ ] 客户详情查看
- [ ] 信用额度管理

### 7.2 采购管理 (2周)

#### 原材料采购
**接口设计**:
- [ ] GET /api/purchase-orders - 获取采购订单列表
- [ ] POST /api/purchase-orders - 创建采购订单
- [ ] PUT /api/purchase-orders/:id - 更新采购订单
- [ ] POST /api/purchase-orders/:id/approve - 审核采购订单
- [ ] POST /api/purchase-receipts - 创建采购入库单
- [ ] PUT /api/inventory/materials/:id - 更新原材料库存

**页面开发**:
- [ ] 采购订单列表页面
- [ ] 采购订单创建/编辑页面
- [ ] 采购订单审核流程
- [ ] 采购入库单页面
- [ ] 供应商选择组件
- [ ] 原材料选择组件

**业务逻辑**:
- [ ] 采购订单状态管理（待审核、已审核、已完成）
- [ ] 采购入库自动更新库存
- [ ] 采购价格记录和管理

### 7.3 简化生产管理 (1.5周)

#### 生产计划
**接口设计**:
- [ ] GET /api/production-plans - 获取生产计划列表
- [ ] POST /api/production-plans - 创建生产计划
- [ ] PUT /api/production-plans/:id - 更新生产计划

**页面开发**:
- [ ] 生产计划列表页面
- [ ] 生产计划创建页面
- [ ] 原材料需求计算
- [ ] 生产进度跟踪

#### 生产完工
**接口设计**:
- [ ] POST /api/production-completions - 记录生产完工
- [ ] PUT /api/inventory/materials/:id/consume - 消耗原材料库存
- [ ] PUT /api/inventory/products/:id/produce - 增加成品库存

**页面开发**:
- [ ] 生产完工记录页面
- [ ] 原材料消耗录入
- [ ] 成品产出录入
- [ ] 生产成本计算

### 7.4 销售管理 (1.5周)

#### 销售订单
**接口设计**:
- [ ] GET /api/sales-orders - 获取销售订单列表
- [ ] POST /api/sales-orders - 创建销售订单
- [ ] PUT /api/sales-orders/:id - 更新销售订单
- [ ] GET /api/inventory/products/:id/available - 检查库存可用性

**页面开发**:
- [ ] 销售订单列表页面
- [ ] 销售订单创建/编辑页面
- [ ] 客户选择组件
- [ ] 成品选择组件
- [ ] 库存可用性检查

#### 销售出库
**接口设计**:
- [ ] POST /api/sales-deliveries - 创建销售出库单
- [ ] PUT /api/inventory/products/:id/deliver - 扣减成品库存

**页面开发**:
- [ ] 销售出库单页面
- [ ] 出库数量录入
- [ ] 自动扣减库存确认

## 8. 验收标准

### 8.1 功能验收
- 供应商和客户信息管理正常
- 采购流程完整可用（订单→审核→入库→更新库存）
- 生产计划可以创建和执行
- 销售订单可以创建并检查库存
- 销售出库可以正常扣减库存

### 8.2 业务验收
- 采购入库后原材料库存正确更新
- 生产完工后原材料库存正确扣减，成品库存正确增加
- 销售出库后成品库存正确扣减
- 各模块数据关联正确

## 9. 依赖关系

### 9.1 前置条件
- 第一阶段完成：用户认证系统、原材料管理、成品管理
- 数据库中已有原材料和成品基础数据

### 9.2 输出成果
- 完整的采购管理流程
- 简化的生产管理功能
- 基础的销售管理功能
- 供应商和客户基础数据
- 为第三阶段提供业务数据和库存基础