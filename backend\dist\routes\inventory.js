"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
const express_1 = __importDefault(require("express"));
const inventoryController_1 = require("../controllers/inventoryController");
const auth_1 = require("../middleware/auth");
const router = express_1.default.Router();
// 所有路由都需要认证
router.use(auth_1.authenticateToken);
// GET /api/inventory - 获取库存列表（原材料和成品）
router.get('/', inventoryController_1.getInventoryList);
// GET /api/inventory/materials - 获取原材料库存列表
router.get('/materials', inventoryController_1.getMaterialsInventory);
// GET /api/inventory/products - 获取成品库存列表
router.get('/products', inventoryController_1.getProductsInventory);
// GET /api/inventory/:itemType/:itemId/history - 获取库存变动历史
router.get('/:itemType/:itemId/history', inventoryController_1.getInventoryHistory);
// GET /api/inventory/alerts - 获取库存预警信息
router.get('/alerts', inventoryController_1.getInventoryAlerts);
// GET /api/inventory/movements - 获取所有库存变动记录（兼容性路由）
router.get('/movements', async (req, res) => {
    try {
        const { item_type, item_id, page = 1, limit = 10 } = req.query;
        if (!item_type || !item_id) {
            return res.status(400).json({
                success: false,
                message: '请提供item_type和item_id参数'
            });
        }
        // 修改req.params以匹配getInventoryHistory的期望格式
        req.params = {
            itemType: item_type,
            itemId: item_id
        };
        // 调用正确的API处理函数
        return (0, inventoryController_1.getInventoryHistory)(req, res);
    }
    catch (error) {
        console.error('获取库存变动记录失败:', error);
        res.status(500).json({
            success: false,
            message: '获取库存变动记录失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
});
exports.default = router;
//# sourceMappingURL=inventory.js.map