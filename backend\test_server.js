const express = require('express');
const cors = require('cors');
const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const app = express();
const PORT = 3000;

// 中间件
app.use(cors());
app.use(express.json());

// 数据库连接
const dbPath = path.join(__dirname, 'data', 'erp.db');
const db = new sqlite3.Database(dbPath);

// 测试路由
app.get('/api/test', (req, res) => {
  res.json({ message: '服务器运行正常', timestamp: new Date().toISOString() });
});

// 获取销售订单
app.get('/api/sales-orders', (req, res) => {
  const query = `
    SELECT so.*, c.name as customer_name
    FROM sales_orders so
    LEFT JOIN customers c ON so.customer_id = c.id
    ORDER BY so.created_at DESC
  `;
  
  db.all(query, (err, rows) => {
    if (err) {
      console.error('查询销售订单错误:', err);
      return res.status(500).json({ success: false, message: '查询失败' });
    }
    
    res.json({
      success: true,
      data: rows,
      total: rows.length
    });
  });
});

// 审核销售订单
app.put('/api/sales-orders/:id/approve', (req, res) => {
  const { id } = req.params;
  
  // 先查询订单状态
  db.get('SELECT * FROM sales_orders WHERE id = ?', [id], (err, order) => {
    if (err) {
      console.error('查询订单错误:', err);
      return res.status(500).json({ success: false, message: '查询失败' });
    }
    
    if (!order) {
      return res.status(404).json({ success: false, message: '订单不存在' });
    }
    
    if (order.status !== 'pending') {
      return res.status(400).json({ success: false, message: '只有待审核状态的订单才能审核' });
    }
    
    // 更新状态为已审核
    db.run('UPDATE sales_orders SET status = ? WHERE id = ?', ['approved', id], function(err) {
      if (err) {
        console.error('更新订单状态错误:', err);
        return res.status(500).json({ success: false, message: '审核失败' });
      }
      
      res.json({
        success: true,
        message: '订单审核成功'
      });
    });
  });
});

app.listen(PORT, () => {
  console.log(`测试服务器运行在端口 ${PORT}`);
  console.log(`API地址: http://localhost:${PORT}`);
});
