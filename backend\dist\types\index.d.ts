export interface User {
    id: number;
    username: string;
    password: string;
    email?: string;
    role: 'admin' | 'user';
    created_at: string;
    updated_at: string;
}
export interface UserCreateInput {
    username: string;
    password: string;
    email?: string;
    role?: 'admin' | 'user';
}
export interface UserLoginInput {
    username: string;
    password: string;
}
export interface Material {
    id: number;
    code: string;
    name: string;
    specification?: string;
    unit: string;
    cost_price: number;
    stock_min: number;
    stock_max: number;
    current_stock: number;
    status: 'active' | 'inactive';
    created_at: string;
    updated_at: string;
}
export interface MaterialCreateInput {
    code: string;
    name: string;
    specification?: string;
    unit: string;
    cost_price?: number;
    stock_min?: number;
    stock_max?: number;
    current_stock?: number;
}
export interface MaterialUpdateInput {
    code?: string;
    name?: string;
    specification?: string;
    unit?: string;
    cost_price?: number;
    stock_min?: number;
    stock_max?: number;
    current_stock?: number;
    status?: 'active' | 'inactive';
}
export interface Product {
    id: number;
    code: string;
    name: string;
    specification?: string;
    unit: string;
    cost_price: number;
    sale_price: number;
    stock_min: number;
    stock_max: number;
    current_stock: number;
    status: 'active' | 'inactive';
    created_at: string;
    updated_at: string;
}
export interface ProductCreateInput {
    code: string;
    name: string;
    specification?: string;
    unit: string;
    cost_price?: number;
    sale_price?: number;
    stock_min?: number;
    stock_max?: number;
    current_stock?: number;
}
export interface ProductUpdateInput {
    code?: string;
    name?: string;
    specification?: string;
    unit?: string;
    cost_price?: number;
    sale_price?: number;
    stock_min?: number;
    stock_max?: number;
    current_stock?: number;
    status?: 'active' | 'inactive';
}
export interface Supplier {
    id: number;
    code: string;
    name: string;
    contact_person?: string;
    phone?: string;
    address?: string;
    settlement_method?: string;
    status: 'active' | 'inactive';
    created_at: string;
    updated_at: string;
}
export interface SupplierCreateInput {
    code: string;
    name: string;
    contact_person?: string;
    phone?: string;
    address?: string;
    settlement_method?: string;
}
export interface SupplierUpdateInput {
    code?: string;
    name?: string;
    contact_person?: string;
    phone?: string;
    address?: string;
    settlement_method?: string;
    status?: 'active' | 'inactive';
}
export interface Customer {
    id: number;
    code: string;
    name: string;
    contact_person?: string;
    phone?: string;
    address?: string;
    credit_limit?: number;
    status: 'active' | 'inactive';
    created_at: string;
    updated_at: string;
}
export interface CustomerCreateInput {
    code: string;
    name: string;
    contact_person?: string;
    phone?: string;
    address?: string;
    credit_limit?: number;
}
export interface CustomerUpdateInput {
    code?: string;
    name?: string;
    contact_person?: string;
    phone?: string;
    address?: string;
    credit_limit?: number;
    status?: 'active' | 'inactive';
}
export interface PurchaseOrder {
    id: number;
    order_no: string;
    supplier_id: number;
    supplier_name?: string;
    order_date: string;
    expected_date?: string;
    total_amount: number;
    status: 'draft' | 'pending' | 'approved' | 'completed' | 'cancelled';
    remark?: string;
    created_at: string;
    updated_at: string;
}
export interface PurchaseOrderItem {
    id: number;
    purchase_order_id: number;
    material_id: number;
    material_code?: string;
    material_name?: string;
    quantity: number;
    unit_price: number;
    total_price: number;
    received_quantity?: number;
    created_at: string;
    updated_at: string;
}
export interface PurchaseOrderCreateInput {
    supplier_id: number;
    order_date: string;
    expected_date?: string;
    remark?: string;
    items: {
        material_id: number;
        quantity: number;
        unit_price: number;
    }[];
}
export interface PurchaseOrderUpdateInput {
    supplier_id?: number;
    order_date?: string;
    expected_date?: string;
    remark?: string;
    status?: 'draft' | 'pending' | 'approved' | 'completed' | 'cancelled';
    items?: {
        id?: number;
        material_id: number;
        quantity: number;
        unit_price: number;
    }[];
}
export interface PurchaseReceipt {
    id: number;
    receipt_no: string;
    purchase_order_id: number;
    purchase_order_no?: string;
    supplier_id: number;
    supplier_name?: string;
    receipt_date: string;
    total_amount: number;
    status: 'draft' | 'confirmed';
    remark?: string;
    created_at: string;
    updated_at: string;
}
export interface PurchaseReceiptItem {
    id: number;
    purchase_receipt_id: number;
    material_id: number;
    material_code?: string;
    material_name?: string;
    quantity: number;
    unit_price: number;
    total_price: number;
    created_at: string;
    updated_at: string;
}
export interface PurchaseReceiptCreateInput {
    purchase_order_id: number;
    receipt_date: string;
    remark?: string;
    items: {
        material_id: number;
        quantity: number;
        unit_price: number;
    }[];
}
export interface SalesOrder {
    id: number;
    order_no: string;
    customer_id: number;
    customer_name?: string;
    order_date: string;
    delivery_date?: string;
    total_amount: number;
    status: 'pending' | 'approved' | 'completed' | 'cancelled';
    remark?: string;
    created_at: string;
    updated_at: string;
}
export interface SalesOrderItem {
    id: number;
    sales_order_id: number;
    product_id: number;
    product_code?: string;
    product_name?: string;
    quantity: number;
    unit_price: number;
    total_price: number;
    delivered_quantity?: number;
    created_at: string;
    updated_at: string;
}
export interface SalesOrderCreateInput {
    customer_id: number;
    order_date: string;
    delivery_date?: string;
    remark?: string;
    items: {
        product_id: number;
        quantity: number;
        unit_price: number;
    }[];
}
export interface SalesOrderUpdateInput {
    customer_id?: number;
    order_date?: string;
    delivery_date?: string;
    remark?: string;
    status?: 'pending' | 'approved' | 'completed' | 'cancelled';
    items?: {
        id?: number;
        product_id: number;
        quantity: number;
        unit_price: number;
    }[];
}
export interface SalesDelivery {
    id: number;
    delivery_no: string;
    sales_order_id: number;
    sales_order_no?: string;
    customer_id: number;
    customer_name?: string;
    delivery_date: string;
    total_amount: number;
    status: 'draft' | 'confirmed';
    remark?: string;
    created_at: string;
    updated_at: string;
}
export interface SalesDeliveryItem {
    id: number;
    sales_delivery_id: number;
    product_id: number;
    product_code?: string;
    product_name?: string;
    quantity: number;
    unit_price: number;
    total_price: number;
    created_at: string;
    updated_at: string;
}
export interface SalesDeliveryCreateInput {
    sales_order_id: number;
    delivery_date: string;
    remark?: string;
    items: {
        product_id: number;
        quantity: number;
        unit_price: number;
    }[];
}
export interface ProductionPlan {
    id: number;
    plan_no: string;
    product_id: number;
    product_code?: string;
    product_name?: string;
    planned_quantity: number;
    actual_quantity?: number;
    plan_date: string;
    start_date?: string;
    completion_date?: string;
    status: 'pending' | 'approved' | 'in_progress' | 'completed' | 'cancelled';
    remark?: string;
    created_at: string;
    updated_at: string;
}
export interface ProductionPlanItem {
    id: number;
    production_plan_id: number;
    material_id: number;
    material_code?: string;
    material_name?: string;
    required_quantity: number;
    consumed_quantity?: number;
    created_at: string;
    updated_at: string;
}
export interface ProductionPlanCreateInput {
    product_id: number;
    planned_quantity: number;
    plan_date: string;
    remark?: string;
    materials: {
        material_id: number;
        required_quantity: number;
    }[];
}
export interface ProductionPlanUpdateInput {
    product_id?: number;
    planned_quantity?: number;
    plan_date?: string;
    start_date?: string;
    completion_date?: string;
    status?: 'pending' | 'approved' | 'in_progress' | 'completed' | 'cancelled';
    remark?: string;
}
export interface ProductionCompletion {
    id: number;
    completion_no: string;
    production_plan_id: number;
    production_plan_no?: string;
    product_id: number;
    product_code?: string;
    product_name?: string;
    completed_quantity: number;
    completion_date: string;
    status: 'draft' | 'confirmed';
    remark?: string;
    created_at: string;
    updated_at: string;
}
export interface ProductionCompletionItem {
    id: number;
    production_completion_id: number;
    material_id: number;
    material_code?: string;
    material_name?: string;
    consumed_quantity: number;
    created_at: string;
    updated_at: string;
}
export interface ProductionCompletionCreateInput {
    production_plan_id: number;
    completed_quantity: number;
    completion_date: string;
    remark?: string;
    materials: {
        material_id: number;
        consumed_quantity: number;
    }[];
}
export interface ApiResponse<T = any> {
    success: boolean;
    message: string;
    data?: T;
    error?: string;
}
export interface PaginationQuery {
    page?: number;
    limit?: number;
    search?: string;
}
export interface PaginatedResponse<T> {
    data: T[];
    total: number;
    page: number;
    limit: number;
    totalPages: number;
}
export interface InventoryMovement {
    id: number;
    item_type: 'material' | 'product';
    item_id: number;
    movement_type: 'in' | 'out' | 'adjust';
    quantity: number;
    before_quantity: number;
    after_quantity: number;
    reference_type?: string;
    reference_id?: number;
    reference_no?: string;
    remark?: string;
    created_at: string;
    created_by?: number;
}
export interface InventoryMovementCreateInput {
    item_type: 'material' | 'product';
    item_id: number;
    movement_type: 'in' | 'out' | 'adjust';
    quantity: number;
    before_quantity: number;
    after_quantity: number;
    reference_type?: string;
    reference_id?: number;
    reference_no?: string;
    remark?: string;
    created_by?: number;
}
export interface InventoryAlert {
    id: number;
    item_type: 'material' | 'product';
    item_id: number;
    alert_type: 'low_stock' | 'high_stock' | 'zero_stock';
    current_stock: number;
    threshold_value?: number;
    status: 'active' | 'resolved';
    created_at: string;
    resolved_at?: string;
    resolved_by?: number;
}
export interface InventoryAlertCreateInput {
    item_type: 'material' | 'product';
    item_id: number;
    alert_type: 'low_stock' | 'high_stock' | 'zero_stock';
    current_stock: number;
    threshold_value?: number;
}
export interface StocktakingTask {
    id: number;
    task_no: string;
    title: string;
    description?: string;
    task_date: string;
    status: 'draft' | 'in_progress' | 'completed' | 'cancelled';
    created_by: number;
    created_at: string;
    updated_at: string;
    completed_at?: string;
}
export interface StocktakingTaskCreateInput {
    title: string;
    description?: string;
    task_date: string;
    created_by: number;
}
export interface StocktakingTaskUpdateInput {
    title?: string;
    description?: string;
    task_date?: string;
    status?: 'draft' | 'in_progress' | 'completed' | 'cancelled';
}
export interface StocktakingItem {
    id: number;
    stocktaking_task_id: number;
    item_type: 'material' | 'product';
    item_id: number;
    system_quantity: number;
    actual_quantity?: number;
    difference_quantity?: number;
    status: 'pending' | 'counted' | 'adjusted';
    remark?: string;
    counted_at?: string;
    counted_by?: number;
    adjusted_at?: string;
    adjusted_by?: number;
    created_at: string;
    updated_at: string;
}
export interface StocktakingItemCreateInput {
    stocktaking_task_id: number;
    item_type: 'material' | 'product';
    item_id: number;
    system_quantity: number;
}
export interface StocktakingItemUpdateInput {
    actual_quantity?: number;
    difference_quantity?: number;
    status?: 'pending' | 'counted' | 'adjusted';
    remark?: string;
    counted_by?: number;
    adjusted_by?: number;
}
export interface InventoryQuery {
    page?: number;
    limit?: number;
    search?: string;
    item_type?: 'material' | 'product';
    alert_status?: 'normal' | 'low_stock' | 'high_stock' | 'zero_stock';
}
export interface InventoryItem {
    id: number;
    code: string;
    name: string;
    specification?: string;
    unit: string;
    current_stock: number;
    stock_min: number;
    stock_max: number;
    alert_status: 'normal' | 'low_stock' | 'high_stock' | 'zero_stock';
    item_type: 'material' | 'product';
    cost_price?: number;
    sale_price?: number;
}
export interface ReportQuery {
    start_date?: string;
    end_date?: string;
    item_type?: 'material' | 'product';
    item_id?: number;
}
export interface PurchaseSummaryReport {
    period: string;
    total_orders: number;
    total_amount: number;
    total_quantity: number;
    supplier_count: number;
    items: {
        material_id: number;
        material_code: string;
        material_name: string;
        quantity: number;
        amount: number;
        unit: string;
    }[];
}
export interface SalesSummaryReport {
    period: string;
    total_orders: number;
    total_amount: number;
    total_quantity: number;
    customer_count: number;
    items: {
        product_id: number;
        product_code: string;
        product_name: string;
        quantity: number;
        amount: number;
        unit: string;
    }[];
}
export interface InventoryMovementReport {
    period: string;
    total_movements: number;
    in_movements: number;
    out_movements: number;
    adjust_movements: number;
    items: {
        item_id: number;
        item_code: string;
        item_name: string;
        item_type: 'material' | 'product';
        in_quantity: number;
        out_quantity: number;
        adjust_quantity: number;
        net_change: number;
        unit: string;
    }[];
}
export interface ProductionCostReport {
    period: string;
    total_productions: number;
    total_cost: number;
    total_output_value: number;
    profit_margin: number;
    items: {
        product_id: number;
        product_code: string;
        product_name: string;
        produced_quantity: number;
        material_cost: number;
        output_value: number;
        unit_cost: number;
        unit: string;
    }[];
}
export interface MaterialConsumptionReport {
    period: string;
    total_consumption: number;
    total_cost: number;
    items: {
        material_id: number;
        material_code: string;
        material_name: string;
        consumed_quantity: number;
        cost: number;
        unit: string;
        usage_rate: number;
    }[];
}
export interface ProductOutputReport {
    period: string;
    total_output: number;
    total_value: number;
    items: {
        product_id: number;
        product_code: string;
        product_name: string;
        output_quantity: number;
        output_value: number;
        unit: string;
    }[];
}
export interface FinancialSummaryReport {
    period: string;
    purchase_amount: number;
    sales_amount: number;
    production_cost: number;
    gross_profit: number;
    profit_margin: number;
    inventory_value: number;
    monthly_data: {
        month: string;
        purchase_amount: number;
        sales_amount: number;
        profit: number;
    }[];
}
//# sourceMappingURL=index.d.ts.map