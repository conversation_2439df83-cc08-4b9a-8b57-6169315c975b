const { checkInventoryAlerts, checkAllInventoryAlerts } = require('./dist/utils/inventoryUtils');
const { initDatabase } = require('./dist/models/database');

async function testAlerts() {
  console.log('开始测试库存预警系统...');

  try {
    // 初始化数据库
    console.log('初始化数据库...');
    await initDatabase();

    // 测试单个物料预警检查
    console.log('\n1. 检查MAT001预警...');
    await checkInventoryAlerts('material', 1);

    console.log('\n2. 检查MAT002预警...');
    await checkInventoryAlerts('material', 2);

    console.log('\n3. 检查PRD001预警...');
    await checkInventoryAlerts('product', 1);

    console.log('\n4. 批量检查所有预警...');
    await checkAllInventoryAlerts();

    console.log('\n预警检查完成！');

  } catch (error) {
    console.error('预警检查失败:', error);
  }
}

testAlerts();
