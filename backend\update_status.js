const sqlite3 = require('sqlite3').verbose();
const path = require('path');

const dbPath = path.join(__dirname, 'data', 'erp.db');
const db = new sqlite3.Database(dbPath);

console.log('更新状态：将所有draft状态改为pending...');

// 更新销售订单状态
db.run("UPDATE sales_orders SET status = 'pending' WHERE status = 'draft'", function(err) {
  if (err) {
    console.error('更新销售订单状态错误:', err);
    return;
  }
  console.log(`更新了 ${this.changes} 个销售订单的状态`);
  
  // 更新生产计划状态
  db.run("UPDATE production_plans SET status = 'pending' WHERE status = 'draft'", function(err) {
    if (err) {
      console.error('更新生产计划状态错误:', err);
      return;
    }
    console.log(`更新了 ${this.changes} 个生产计划的状态`);
    
    // 验证更新结果
    console.log('\n验证更新结果...');
    
    db.all("SELECT id, order_no, status FROM sales_orders ORDER BY id", (err, rows) => {
      if (err) {
        console.error('查询错误:', err);
        return;
      }
      
      console.log('销售订单:');
      rows.forEach(row => {
        console.log(`ID: ${row.id}, 订单号: ${row.order_no}, 状态: ${row.status}`);
      });
      
      db.all("SELECT id, plan_no, status FROM production_plans ORDER BY id", (err, rows) => {
        if (err) {
          console.error('查询错误:', err);
          return;
        }
        
        console.log('\n生产计划:');
        rows.forEach(row => {
          console.log(`ID: ${row.id}, 计划号: ${row.plan_no}, 状态: ${row.status}`);
        });
        
        db.close();
        console.log('\n状态更新完成！');
      });
    });
  });
});
