{"name": "backend", "version": "1.0.0", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "nodemon --exec ts-node src/index.ts", "test": "echo \"Error: no test specified\" && exit 1"}, "keywords": [], "author": "", "license": "ISC", "description": "", "dependencies": {"axios": "^1.11.0", "bcryptjs": "^3.0.2", "cors": "^2.8.5", "dotenv": "^17.2.1", "echarts": "^5.5.1", "express": "^5.1.0", "jsonwebtoken": "^9.0.2", "playwright": "^1.54.2", "sqlite3": "^5.1.7"}, "devDependencies": {"@types/bcryptjs": "^2.4.6", "@types/cors": "^2.8.19", "@types/express": "^5.0.3", "@types/jsonwebtoken": "^9.0.10", "@types/node": "^24.1.0", "nodemon": "^3.1.10", "ts-node": "^10.9.2", "typescript": "^5.9.2"}}