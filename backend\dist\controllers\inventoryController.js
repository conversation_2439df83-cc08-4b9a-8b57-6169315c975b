"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getInventoryList = getInventoryList;
exports.getMaterialsInventory = getMaterialsInventory;
exports.getProductsInventory = getProductsInventory;
exports.getInventoryHistory = getInventoryHistory;
exports.getInventoryAlerts = getInventoryAlerts;
exports.createInventoryMovement = createInventoryMovement;
exports.createInventoryAlert = createInventoryAlert;
const database_1 = require("../models/database");
// 简单的内存缓存
const cache = new Map();
const CACHE_TTL = 5 * 60 * 1000; // 5分钟缓存
function getCacheKey(prefix, params) {
    return `${prefix}:${JSON.stringify(params)}`;
}
function getFromCache(key) {
    const cached = cache.get(key);
    if (cached && Date.now() - cached.timestamp < CACHE_TTL) {
        return cached.data;
    }
    cache.delete(key);
    return null;
}
function setCache(key, data) {
    cache.set(key, { data, timestamp: Date.now() });
}
function clearCacheByPrefix(prefix) {
    for (const key of cache.keys()) {
        if (key.startsWith(prefix)) {
            cache.delete(key);
        }
    }
}
// 获取库存列表（原材料和成品）
async function getInventoryList(req, res) {
    try {
        const query = req.query;
        const page = parseInt(String(query.page || '1')) || 1;
        const limit = parseInt(String(query.limit || '10')) || 10;
        const search = query.search || '';
        const itemType = query.item_type;
        const alertStatus = query.alert_status;
        const offset = (page - 1) * limit;
        const db = (0, database_1.getDatabase)();
        // 构建查询条件
        let whereConditions = [];
        let params = [];
        if (search) {
            whereConditions.push('(code LIKE ? OR name LIKE ? OR specification LIKE ?)');
            const searchPattern = `%${search}%`;
            params.push(searchPattern, searchPattern, searchPattern);
        }
        // 构建联合查询SQL
        let unionQueries = [];
        if (!itemType || itemType === 'material') {
            let materialWhere = "status = 'active'";
            if (whereConditions.length > 0) {
                materialWhere += ' AND ' + whereConditions.join(' AND ');
            }
            unionQueries.push(`
        SELECT 
          id, code, name, specification, unit, current_stock, 
          stock_min, stock_max, cost_price, NULL as sale_price,
          'material' as item_type,
          CASE
            WHEN current_stock = 0 THEN 'zero_stock'
            WHEN stock_min > 0 AND current_stock < stock_min THEN 'low_stock'
            WHEN stock_max > 0 AND current_stock > stock_max THEN 'high_stock'
            ELSE 'normal'
          END as alert_status
        FROM materials 
        WHERE ${materialWhere}
      `);
        }
        if (!itemType || itemType === 'product') {
            let productWhere = "status = 'active'";
            if (whereConditions.length > 0) {
                productWhere += ' AND ' + whereConditions.join(' AND ');
            }
            unionQueries.push(`
        SELECT 
          id, code, name, specification, unit, current_stock, 
          stock_min, stock_max, cost_price, sale_price,
          'product' as item_type,
          CASE
            WHEN current_stock = 0 THEN 'zero_stock'
            WHEN stock_min > 0 AND current_stock < stock_min THEN 'low_stock'
            WHEN stock_max > 0 AND current_stock > stock_max THEN 'high_stock'
            ELSE 'normal'
          END as alert_status
        FROM products 
        WHERE ${productWhere}
      `);
        }
        let finalQuery = unionQueries.join(' UNION ALL ');
        // 添加预警状态过滤
        if (alertStatus && alertStatus !== 'normal') {
            finalQuery = `SELECT * FROM (${finalQuery}) WHERE alert_status = ?`;
            params.push(alertStatus);
        }
        // 获取总数
        const countQuery = `SELECT COUNT(*) as total FROM (${finalQuery})`;
        const totalResult = await new Promise((resolve, reject) => {
            db.get(countQuery, params, (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        // 获取分页数据
        finalQuery += ' ORDER BY item_type, code LIMIT ? OFFSET ?';
        const dataParams = [...params, limit, offset];
        const items = await new Promise((resolve, reject) => {
            db.all(finalQuery, dataParams, (err, rows) => {
                if (err)
                    reject(err);
                else
                    resolve(rows);
            });
        });
        const response = {
            data: items,
            total: totalResult.total,
            page,
            limit,
            totalPages: Math.ceil(totalResult.total / limit)
        };
        res.json({
            success: true,
            message: '获取库存列表成功',
            data: response
        });
    }
    catch (error) {
        console.error('获取库存列表失败:', error);
        res.status(500).json({
            success: false,
            message: '获取库存列表失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
}
// 获取原材料库存列表
async function getMaterialsInventory(req, res) {
    try {
        const query = req.query;
        const page = parseInt(String(query.page || '1')) || 1;
        const limit = parseInt(String(query.limit || '10')) || 10;
        const search = query.search || '';
        const alertStatus = query.alert_status;
        // 检查缓存
        const cacheKey = getCacheKey('materials_inventory', { page, limit, search, alertStatus });
        const cachedResult = getFromCache(cacheKey);
        if (cachedResult) {
            return res.json(cachedResult);
        }
        const offset = (page - 1) * limit;
        const db = (0, database_1.getDatabase)();
        // 构建查询条件
        let whereClause = "WHERE status = 'active'";
        let params = [];
        if (search) {
            whereClause += ' AND (code LIKE ? OR name LIKE ? OR specification LIKE ?)';
            const searchPattern = `%${search}%`;
            params.push(searchPattern, searchPattern, searchPattern);
        }
        if (alertStatus && alertStatus !== 'normal') {
            if (alertStatus === 'zero_stock') {
                whereClause += ' AND current_stock = 0';
            }
            else if (alertStatus === 'low_stock') {
                whereClause += ' AND current_stock > 0 AND current_stock < stock_min';
            }
            else if (alertStatus === 'high_stock') {
                whereClause += ' AND current_stock > stock_max';
            }
        }
        // 获取总数
        const countQuery = `SELECT COUNT(*) as total FROM materials ${whereClause}`;
        const totalResult = await new Promise((resolve, reject) => {
            db.get(countQuery, params, (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        // 获取数据
        const dataQuery = `
      SELECT 
        id, code, name, specification, unit, current_stock, 
        stock_min, stock_max, cost_price,
        CASE 
          WHEN current_stock = 0 THEN 'zero_stock'
          WHEN current_stock < stock_min THEN 'low_stock'
          WHEN current_stock > stock_max THEN 'high_stock'
          ELSE 'normal'
        END as alert_status,
        'material' as item_type
      FROM materials 
      ${whereClause}
      ORDER BY code
      LIMIT ? OFFSET ?
    `;
        const materials = await new Promise((resolve, reject) => {
            db.all(dataQuery, [...params, limit, offset], (err, rows) => {
                if (err)
                    reject(err);
                else
                    resolve(rows);
            });
        });
        const response = {
            data: materials,
            total: totalResult.total,
            page,
            limit,
            totalPages: Math.ceil(totalResult.total / limit)
        };
        const result = {
            success: true,
            message: '获取原材料库存成功',
            data: response
        };
        // 设置缓存
        setCache(cacheKey, result);
        res.json(result);
    }
    catch (error) {
        console.error('获取原材料库存失败:', error);
        res.status(500).json({
            success: false,
            message: '获取原材料库存失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
}
// 获取成品库存列表
async function getProductsInventory(req, res) {
    try {
        const query = req.query;
        const page = parseInt(String(query.page || '1')) || 1;
        const limit = parseInt(String(query.limit || '10')) || 10;
        const search = query.search || '';
        const alertStatus = query.alert_status;
        // 检查缓存
        const cacheKey = getCacheKey('products_inventory', { page, limit, search, alertStatus });
        const cachedResult = getFromCache(cacheKey);
        if (cachedResult) {
            return res.json(cachedResult);
        }
        const offset = (page - 1) * limit;
        const db = (0, database_1.getDatabase)();
        // 构建查询条件
        let whereClause = "WHERE status = 'active'";
        let params = [];
        if (search) {
            whereClause += ' AND (code LIKE ? OR name LIKE ? OR specification LIKE ?)';
            const searchPattern = `%${search}%`;
            params.push(searchPattern, searchPattern, searchPattern);
        }
        if (alertStatus && alertStatus !== 'normal') {
            if (alertStatus === 'zero_stock') {
                whereClause += ' AND current_stock = 0';
            }
            else if (alertStatus === 'low_stock') {
                whereClause += ' AND current_stock > 0 AND current_stock < stock_min';
            }
            else if (alertStatus === 'high_stock') {
                whereClause += ' AND current_stock > stock_max';
            }
        }
        // 获取总数
        const countQuery = `SELECT COUNT(*) as total FROM products ${whereClause}`;
        const totalResult = await new Promise((resolve, reject) => {
            db.get(countQuery, params, (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        // 获取数据
        const dataQuery = `
      SELECT 
        id, code, name, specification, unit, current_stock, 
        stock_min, stock_max, cost_price, sale_price,
        CASE 
          WHEN current_stock = 0 THEN 'zero_stock'
          WHEN current_stock < stock_min THEN 'low_stock'
          WHEN current_stock > stock_max THEN 'high_stock'
          ELSE 'normal'
        END as alert_status,
        'product' as item_type
      FROM products 
      ${whereClause}
      ORDER BY code
      LIMIT ? OFFSET ?
    `;
        const products = await new Promise((resolve, reject) => {
            db.all(dataQuery, [...params, limit, offset], (err, rows) => {
                if (err)
                    reject(err);
                else
                    resolve(rows);
            });
        });
        const response = {
            data: products,
            total: totalResult.total,
            page,
            limit,
            totalPages: Math.ceil(totalResult.total / limit)
        };
        res.json({
            success: true,
            message: '获取成品库存成功',
            data: response
        });
    }
    catch (error) {
        console.error('获取成品库存失败:', error);
        res.status(500).json({
            success: false,
            message: '获取成品库存失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
}
// 获取库存变动历史
async function getInventoryHistory(req, res) {
    try {
        const { itemType, itemId } = req.params;
        const { page = 1, limit = 10 } = req.query;
        const offset = (Number(page) - 1) * Number(limit);
        const db = (0, database_1.getDatabase)();
        // 获取总数
        const countQuery = `
      SELECT COUNT(*) as total
      FROM inventory_movements
      WHERE item_type = ? AND item_id = ?
    `;
        const totalResult = await new Promise((resolve, reject) => {
            db.get(countQuery, [itemType, itemId], (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        // 获取变动历史
        const dataQuery = `
      SELECT
        im.*,
        u.username as created_by_name
      FROM inventory_movements im
      LEFT JOIN users u ON im.created_by = u.id
      WHERE im.item_type = ? AND im.item_id = ?
      ORDER BY im.created_at DESC
      LIMIT ? OFFSET ?
    `;
        const movements = await new Promise((resolve, reject) => {
            db.all(dataQuery, [itemType, itemId, Number(limit), offset], (err, rows) => {
                if (err)
                    reject(err);
                else
                    resolve(rows);
            });
        });
        const response = {
            data: movements,
            total: totalResult.total,
            page: Number(page),
            limit: Number(limit),
            totalPages: Math.ceil(totalResult.total / Number(limit))
        };
        res.json({
            success: true,
            message: '获取库存变动历史成功',
            data: response
        });
    }
    catch (error) {
        console.error('获取库存变动历史失败:', error);
        res.status(500).json({
            success: false,
            message: '获取库存变动历史失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
}
// 获取库存预警信息
async function getInventoryAlerts(req, res) {
    try {
        const { page = 1, limit = 10, status = 'active' } = req.query;
        const offset = (Number(page) - 1) * Number(limit);
        const db = (0, database_1.getDatabase)();
        // 构建查询条件
        let whereClause = 'WHERE ia.status = ?';
        const params = [status];
        // 获取总数
        const countQuery = `
      SELECT COUNT(*) as total
      FROM inventory_alerts ia
      ${whereClause}
    `;
        const totalResult = await new Promise((resolve, reject) => {
            db.get(countQuery, params, (err, row) => {
                if (err)
                    reject(err);
                else
                    resolve(row);
            });
        });
        // 获取预警数据
        const dataQuery = `
      SELECT
        ia.*,
        CASE
          WHEN ia.item_type = 'material' THEN m.code
          WHEN ia.item_type = 'product' THEN p.code
        END as item_code,
        CASE
          WHEN ia.item_type = 'material' THEN m.name
          WHEN ia.item_type = 'product' THEN p.name
        END as item_name,
        CASE
          WHEN ia.item_type = 'material' THEN m.unit
          WHEN ia.item_type = 'product' THEN p.unit
        END as item_unit
      FROM inventory_alerts ia
      LEFT JOIN materials m ON ia.item_type = 'material' AND ia.item_id = m.id
      LEFT JOIN products p ON ia.item_type = 'product' AND ia.item_id = p.id
      ${whereClause}
      ORDER BY ia.created_at DESC
      LIMIT ? OFFSET ?
    `;
        const alerts = await new Promise((resolve, reject) => {
            db.all(dataQuery, [...params, Number(limit), offset], (err, rows) => {
                if (err)
                    reject(err);
                else
                    resolve(rows);
            });
        });
        const response = {
            data: alerts,
            total: totalResult.total,
            page: Number(page),
            limit: Number(limit),
            totalPages: Math.ceil(totalResult.total / Number(limit))
        };
        res.json({
            success: true,
            message: '获取库存预警信息成功',
            data: response
        });
    }
    catch (error) {
        console.error('获取库存预警信息失败:', error);
        res.status(500).json({
            success: false,
            message: '获取库存预警信息失败',
            error: error instanceof Error ? error.message : '未知错误'
        });
    }
}
// 创建库存变动记录
async function createInventoryMovement(movementData) {
    const db = (0, database_1.getDatabase)();
    const sql = `
    INSERT INTO inventory_movements (
      item_type, item_id, movement_type, quantity, before_quantity,
      after_quantity, reference_type, reference_id, reference_no,
      remark, created_by
    ) VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
  `;
    const params = [
        movementData.item_type,
        movementData.item_id,
        movementData.movement_type,
        movementData.quantity,
        movementData.before_quantity,
        movementData.after_quantity,
        movementData.reference_type || null,
        movementData.reference_id || null,
        movementData.reference_no || null,
        movementData.remark || null,
        movementData.created_by || null
    ];
    return new Promise((resolve, reject) => {
        db.run(sql, params, function (err) {
            if (err)
                reject(err);
            else
                resolve(this.lastID);
        });
    });
}
// 创建库存预警记录
async function createInventoryAlert(alertData) {
    const db = (0, database_1.getDatabase)();
    const sql = `
    INSERT INTO inventory_alerts (
      item_type, item_id, alert_type, current_stock, threshold_value
    ) VALUES (?, ?, ?, ?, ?)
  `;
    const params = [
        alertData.item_type,
        alertData.item_id,
        alertData.alert_type,
        alertData.current_stock,
        alertData.threshold_value || null
    ];
    return new Promise((resolve, reject) => {
        db.run(sql, params, function (err) {
            if (err)
                reject(err);
            else
                resolve(this.lastID);
        });
    });
}
//# sourceMappingURL=inventoryController.js.map